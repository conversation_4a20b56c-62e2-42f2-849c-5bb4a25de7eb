/**
 * 动画混合器
 * 用于混合多个动画片段
 */
import * as THREE from 'three';
import type { AnimationClip } from './AnimationClip';
import { Animator } from './Animator';
import type { Entity } from '../core/Entity';
import { EventEmitter } from '../utils/EventEmitter';
import { AnimationClipAdapter } from './utils/AnimationClipAdapter';
import { AnimationMask } from './utils/AnimationMask';
import { AnimationSubClip } from './utils/AnimationSubClip';
import { SubClip } from './SubClip';
import { PhysicsAnimationIntegration } from './PhysicsAnimationIntegration';
import { InputAnimationIntegration } from './InputAnimationIntegration';
import { BlendPerformanceMonitor } from './utils/BlendPerformanceMonitor';
import { AnimationEventSystem, AnimationEventType } from './utils/AnimationEventSystem';
import { AnimationCompressor } from './utils/AnimationCompressor';
import { AnimationPreloader } from './utils/AnimationPreloader';
import { AnimationSynchronizer } from './utils/AnimationSynchronizer';
import { AnimationDebugger, DebugMode } from './utils/AnimationDebugger';
import { AnimationQualityController, QualityLevel } from './utils/AnimationQualityController';

/**
 * 混合模式
 */
export enum BlendMode {
  /** 覆盖 - 完全替换之前的动画 */
  OVERRIDE = 'override',
  /** 叠加 - 在之前的动画基础上叠加 */
  ADDITIVE = 'additive',
  /** 乘法 - 将当前动画与之前的动画相乘 */
  MULTIPLY = 'multiply',
  /** 差值 - 计算当前动画与之前动画的差值 */
  DIFFERENCE = 'difference',
  /** 平均 - 计算当前动画与之前动画的平均值 */
  AVERAGE = 'average',
  /** 最大值 - 取当前动画与之前动画中的最大值 */
  MAX = 'max',
  /** 最小值 - 取当前动画与之前动画中的最小值 */
  MIN = 'min',
  /** 交叉淡入淡出 - 在两个动画之间平滑过渡 */
  CROSS_FADE = 'crossFade',
  /** 混合树 - 基于参数混合多个动画 */
  BLEND_TREE = 'blendTree',
  /** 加权 - 根据自定义权重混合多个动画 */
  WEIGHTED = 'weighted',
  /** 分层 - 按层次混合动画 */
  LAYERED = 'layered',
  /** 序列 - 按顺序播放多个动画 */
  SEQUENTIAL = 'sequential'
}

/**
 * 混合曲线类型
 */
export enum BlendCurveType {
  /** 线性 */
  LINEAR = 'linear',
  /** 缓入 */
  EASE_IN = 'easeIn',
  /** 缓出 */
  EASE_OUT = 'easeOut',
  /** 缓入缓出 */
  EASE_IN_OUT = 'easeInOut',
  /** 弹性 */
  ELASTIC = 'elastic',
  /** 弹跳 */
  BOUNCE = 'bounce',
  /** 正弦 */
  SINE = 'sine',
  /** 指数 */
  EXPONENTIAL = 'exponential',
  /** 圆形 */
  CIRCULAR = 'circular',
  /** 二次方 */
  QUADRATIC = 'quadratic',
  /** 三次方 */
  CUBIC = 'cubic',
  /** 四次方 */
  QUARTIC = 'quartic',
  /** 五次方 */
  QUINTIC = 'quintic',
  /** 自定义 */
  CUSTOM = 'custom'
}

/**
 * 混合层
 */
export interface BlendLayer {
  /** 动画片段名称 */
  clipName: string;
  /** 权重 */
  weight: number;
  /** 时间缩放 */
  timeScale: number;
  /** 混合模式 */
  blendMode: BlendMode;
  /** 遮罩 */
  mask?: string[];
  /** 交叉淡入淡出时间（秒） */
  crossFadeTime?: number;
  /** 混合树参数 */
  blendTreeParams?: BlendTreeParams;
  /** 权重参数 */
  weightParams?: WeightedBlendParams;
  /** 层次参数 */
  layerParams?: LayeredBlendParams;
  /** 序列参数 */
  sequenceParams?: SequentialBlendParams;
}

/**
 * 混合树参数
 */
export interface BlendTreeParams {
  /** 参数名称 */
  paramName: string;
  /** 参数值 */
  paramValue: number;
  /** 节点列表 */
  nodes: BlendTreeNode[];
  /** 混合类型 */
  blendType: '1D' | '2D' | 'Direct';
}

/**
 * 混合树节点
 */
export interface BlendTreeNode {
  /** 动画片段名称 */
  clipName: string;
  /** 阈值 */
  threshold: number | { x: number; y: number };
  /** 权重 */
  weight: number;
}

/**
 * 加权混合参数
 */
export interface WeightedBlendParams {
  /** 权重列表 */
  weights: { [clipName: string]: number };
  /** 归一化 */
  normalize: boolean;
}

/**
 * 分层混合参数
 */
export interface LayeredBlendParams {
  /** 层次列表 */
  layers: {
    /** 动画片段名称 */
    clipName: string;
    /** 层次索引 */
    layerIndex: number;
    /** 权重 */
    weight: number;
    /** 遮罩 */
    mask?: string[];
  }[];
}

/**
 * 序列混合参数
 */
export interface SequentialBlendParams {
  /** 序列列表 */
  sequence: {
    /** 动画片段名称 */
    clipName: string;
    /** 持续时间（秒） */
    duration: number;
    /** 过渡时间（秒） */
    transitionTime: number;
  }[];
  /** 当前索引 */
  currentIndex: number;
  /** 是否循环 */
  loop: boolean;
}

/**
 * 混合事件类型
 */
export enum BlendEventType {
  /** 混合开始 */
  BLEND_START = 'blendStart',
  /** 混合更新 */
  BLEND_UPDATE = 'blendUpdate',
  /** 混合结束 */
  BLEND_END = 'blendEnd',
  /** 混合空间更新 */
  BLEND_SPACE_UPDATE = 'blendSpaceUpdate',
  /** 遮罩应用 */
  MASK_APPLIED = 'maskApplied',
  /** 子片段创建 */
  SUBCLIP_CREATED = 'subclipCreated',
  /** 物理集成 */
  PHYSICS_INTEGRATED = 'physicsIntegrated',
  /** 输入集成 */
  INPUT_INTEGRATED = 'inputIntegrated'
}

/**
 * 动画混合器
 */
export class AnimationBlender {
  /** 动画控制器 */
  private animator: Animator;
  /** 混合层列表 */
  private layers: BlendLayer[] = [];
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否启用 */
  private enabled: boolean = true;
  /** 当前混合时间 */
  private blendTime: number = 0;
  /** 目标混合时间 */
  private targetBlendTime: number = 0;
  /** 是否正在混合 */
  private isBlending: boolean = false;
  /** 混合曲线类型 */
  private blendCurveType: BlendCurveType = BlendCurveType.LINEAR;
  /** 自定义混合曲线 */
  private customBlendCurve?: (t: number) => number;
  /** 遮罩映射 */
  private masks: Map<string, AnimationMask> = new Map();
  /** 子片段映射 */
  private subClips: Map<string, SubClip> = new Map();
  /** 高级子片段映射 */
  private advancedSubClips: Map<string, AnimationSubClip> = new Map();
  /** 物理动画集成 */
  private physicsIntegration?: PhysicsAnimationIntegration;
  /** 输入动画集成 */
  private inputIntegration?: InputAnimationIntegration;

  /** 动画事件系统 */
  private eventSystem: AnimationEventSystem;
  /** 动画压缩器 */
  private compressor: AnimationCompressor;
  /** 动画预加载器 */
  private preloader: AnimationPreloader;
  /** 动画同步器 */
  private synchronizer: AnimationSynchronizer | null = null;
  /** 动画调试器 */
  private debugger: AnimationDebugger;
  /** 质量控制器 */
  private qualityController: AnimationQualityController;

  /** 混合质量 */
  private blendQuality: number = 1.0;

  // 缓存
  /** 曲线计算缓存 */
  private curveCache: Map<string, Map<number, number>> = new Map();
  /** 混合权重缓存 */
  private weightCache: Map<string, number[]> = new Map();
  /** 对象池 */
  private objectPool: Map<string, any[]> = new Map();

  // 性能优化配置
  /** 是否启用缓存 */
  private cacheEnabled: boolean = true;
  /** 缓存精度 */
  private cacheResolution: number = 1000;
  /** 是否启用批处理 */
  private batchProcessingEnabled: boolean = true;
  /** 是否启用对象池 */
  private objectPoolEnabled: boolean = true;

  /**
   * 构造函数
   * @param animator 动画控制器
   * @param config 配置选项
   */
  constructor(animator: Animator, config: any = {}) {
    this.animator = animator;

    // 初始化事件系统
    this.eventSystem = AnimationEventSystem.getInstance({
      enabled: true,
      debug: config.debug || false
    });

    // 初始化压缩器
    this.compressor = new AnimationCompressor({
      enableQuantization: config.enableCompression || false,
      positionPrecision: 3,
      rotationPrecision: 4
    });

    // 初始化预加载器
    this.preloader = AnimationPreloader.getInstance({
      maxConcurrent: 4,
      enableCompression: config.enableCompression || false,
      debug: config.debug || false
    });

    // 初始化调试器
    this.debugger = AnimationDebugger.getInstance({
      mode: config.debug ? DebugMode.BASIC : DebugMode.NONE,
      enableConsole: config.debug || false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring || false
    });

    // 初始化质量控制器
    this.qualityController = AnimationQualityController.getInstance({
      enableAutoAdjustment: config.enableQualityControl || false,
      debug: config.debug || false
    });

    // 启动事件系统
    this.eventSystem.start();

    // 启动调试器
    if (config.debug) {
      this.debugger.enable(DebugMode.BASIC);
    }

    // 启动质量控制器性能监控
    if (config.enableQualityControl) {
      this.qualityController.startPerformanceMonitoring();
    }

    // 设置事件监听
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听质量变化事件
    this.qualityController.on('qualityChanged', (data) => {
      this.debugger.log('info', 'quality', `质量等级已更改: ${data.oldQuality} -> ${data.newQuality}`, data.settings);
      this.applyQualitySettings(data.settings);
    });

    // 监听性能更新事件
    this.qualityController.on('performanceUpdate', (metrics) => {
      this.debugger.logPerformanceMetrics(metrics);
    });

    // 监听动画事件
    this.eventSystem.on(AnimationEventType.ANIMATION_START, (data) => {
      this.debugger.logAnimationStart(data.animationName, data.duration);
    });

    this.eventSystem.on(AnimationEventType.ANIMATION_END, (data) => {
      this.debugger.logAnimationEnd(data.animationName, data.time);
    });

    this.eventSystem.on(AnimationEventType.BLEND_START, (data) => {
      this.debugger.logBlendStart(data.data?.fromAnimation || '', data.data?.toAnimation || '', data.data?.blendTime || 0);
    });

    this.eventSystem.on(AnimationEventType.BLEND_END, (data) => {
      this.debugger.logBlendEnd(data.data?.fromAnimation || '', data.data?.toAnimation || '');
    });
  }

  /**
   * 应用质量设置
   * @param settings 质量设置
   */
  private applyQualitySettings(settings: any): void {
    // 应用混合层数限制
    if (settings.maxBlendLayers && this.layers.length > settings.maxBlendLayers) {
      // 移除权重最低的层
      this.layers.sort((a, b) => a.weight - b.weight);
      this.layers = this.layers.slice(-settings.maxBlendLayers);
    }

    // 应用混合质量设置
    if (settings.blendQuality !== undefined) {
      // 这里可以调整混合算法的精度
      this.blendQuality = settings.blendQuality;
    }

    // 应用缓存设置
    if (settings.enableCaching !== undefined) {
      this.cacheEnabled = settings.enableCaching;
    }
  }

  /**
   * 添加混合层
   * @param clipName 动画片段名称
   * @param weight 权重
   * @param blendMode 混合模式
   * @param timeScale 时间缩放
   * @param mask 遮罩
   * @returns 混合层索引
   */
  public addLayer(
    clipName: string,
    weight: number = 1.0,
    blendMode: BlendMode = BlendMode.OVERRIDE,
    timeScale: number = 1.0,
    mask?: string[]
  ): number {
    // 检查动画片段是否存在
    if (!this.animator.getClip(clipName)) {
      console.warn(`动画片段 "${clipName}" 不存在`);
      return -1;
    }

    // 创建混合层
    const layer: BlendLayer = {
      clipName,
      weight,
      timeScale,
      blendMode,
      mask
    };

    // 添加到列表
    this.layers.push(layer);

    return this.layers.length - 1;
  }

  /**
   * 移除混合层
   * @param index 混合层索引
   * @returns 是否成功移除
   */
  public removeLayer(index: number): boolean {
    if (index < 0 || index >= this.layers.length) {
      return false;
    }

    this.layers.splice(index, 1);
    return true;
  }

  /**
   * 设置混合层权重
   * @param index 混合层索引
   * @param weight 权重
   * @param blendTime 混合时间（秒）
   * @returns 是否成功设置
   */
  public setLayerWeight(index: number, weight: number, blendTime: number = 0): boolean {
    if (index < 0 || index >= this.layers.length) {
      return false;
    }

    // 如果混合时间为0，则直接设置权重
    if (blendTime <= 0) {
      this.layers[index].weight = weight;
      return true;
    }

    // 否则，开始混合
    this.startBlending(blendTime);

    // 设置目标权重
    this.layers[index].weight = weight;

    return true;
  }

  /**
   * 设置混合层时间缩放
   * @param index 混合层索引
   * @param timeScale 时间缩放
   * @returns 是否成功设置
   */
  public setLayerTimeScale(index: number, timeScale: number): boolean {
    if (index < 0 || index >= this.layers.length) {
      return false;
    }

    this.layers[index].timeScale = timeScale;
    return true;
  }

  /**
   * 设置混合层混合模式
   * @param index 混合层索引
   * @param blendMode 混合模式
   * @returns 是否成功设置
   */
  public setLayerBlendMode(index: number, blendMode: BlendMode): boolean {
    if (index < 0 || index >= this.layers.length) {
      return false;
    }

    this.layers[index].blendMode = blendMode;
    return true;
  }

  /**
   * 设置混合层遮罩
   * @param index 混合层索引
   * @param mask 遮罩
   * @returns 是否成功设置
   */
  public setLayerMask(index: number, mask?: string[]): boolean {
    if (index < 0 || index >= this.layers.length) {
      return false;
    }

    this.layers[index].mask = mask;
    return true;
  }

  /**
   * 获取混合层
   * @param index 混合层索引
   * @returns 混合层
   */
  public getLayer(index: number): BlendLayer | null {
    if (index < 0 || index >= this.layers.length) {
      return null;
    }

    return this.layers[index];
  }

  /**
   * 获取所有混合层
   * @returns 混合层列表
   */
  public getLayers(): BlendLayer[] {
    return [...this.layers];
  }

  /**
   * 清空所有混合层
   */
  public clearLayers(): void {
    this.layers = [];
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 获取是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置混合曲线类型
   * @param type 混合曲线类型
   * @param customCurve 自定义混合曲线
   */
  public setBlendCurveType(type: BlendCurveType, customCurve?: (t: number) => number): void {
    this.blendCurveType = type;
    this.customBlendCurve = customCurve;
  }

  /**
   * 使用预设曲线
   * @param presetName 预设名称
   */
  public usePresetCurve(presetName: string): void {
    this.blendCurveType = BlendCurveType.CUSTOM;
    this.customBlendCurve = this.createPresetCurve(presetName);
  }

  /**
   * 获取混合曲线类型
   * @returns 混合曲线类型
   */
  public getBlendCurveType(): BlendCurveType {
    return this.blendCurveType;
  }

  /**
   * 设置自定义混合曲线
   * @param curve 自定义混合曲线函数
   */
  public setCustomBlendCurve(curve: (t: number) => number): void {
    this.blendCurveType = BlendCurveType.CUSTOM;
    this.customBlendCurve = curve;
  }

  /**
   * 获取自定义混合曲线
   * @returns 自定义混合曲线函数
   */
  public getCustomBlendCurve(): ((t: number) => number) | undefined {
    return this.customBlendCurve;
  }

  /**
   * 创建贝塞尔曲线混合函数
   * @param x1 第一个控制点的x坐标
   * @param y1 第一个控制点的y坐标
   * @param x2 第二个控制点的x坐标
   * @param y2 第二个控制点的y坐标
   * @returns 贝塞尔曲线混合函数
   */
  public createBezierCurve(x1: number, y1: number, x2: number, y2: number): (t: number) => number {
    // 三次贝塞尔曲线
    return (t: number) => {
      // 确保t在0-1范围内
      t = Math.max(0, Math.min(1, t));

      // 计算贝塞尔曲线点
      const cx = 3 * x1;
      const bx = 3 * (x2 - x1) - cx;
      const ax = 1 - cx - bx;

      const cy = 3 * y1;
      const by = 3 * (y2 - y1) - cy;
      const ay = 1 - cy - by;

      // 计算x(t)
      const tCubed = t * t * t;
      const tSquared = t * t;
      const xt = ax * tCubed + bx * tSquared + cx * t;

      // 使用牛顿迭代法找到对应的t值
      let u = t;
      for (let i = 0; i < 5; i++) {
        const xCubed = u * u * u;
        const xSquared = u * u;
        const xValue = ax * xCubed + bx * xSquared + cx * u;

        if (Math.abs(xValue - xt) < 0.001) {
          break;
        }

        const xDerivative = 3 * ax * xSquared + 2 * bx * u + cx;
        if (Math.abs(xDerivative) < 0.0001) {
          break;
        }

        u = u - (xValue - xt) / xDerivative;
      }

      // 计算y(u)
      const uCubed = u * u * u;
      const uSquared = u * u;
      return ay * uCubed + by * uSquared + cy * u;
    };
  }

  /**
   * 创建预设混合曲线
   * @param name 预设名称
   * @returns 混合曲线函数
   */
  public createPresetCurve(name: string): (t: number) => number {
    switch (name) {
      case 'easeInQuad':
        return (t: number) => t * t;

      case 'easeOutQuad':
        return (t: number) => t * (2 - t);

      case 'easeInOutQuad':
        return (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;

      case 'easeInCubic':
        return (t: number) => t * t * t;

      case 'easeOutCubic':
        return (t: number) => (--t) * t * t + 1;

      case 'easeInOutCubic':
        return (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;

      case 'easeInQuart':
        return (t: number) => t * t * t * t;

      case 'easeOutQuart':
        return (t: number) => 1 - (--t) * t * t * t;

      case 'easeInOutQuart':
        return (t: number) => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t;

      case 'easeInQuint':
        return (t: number) => t * t * t * t * t;

      case 'easeOutQuint':
        return (t: number) => 1 + (--t) * t * t * t * t;

      case 'easeInOutQuint':
        return (t: number) => t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t;

      case 'easeInSine':
        return (t: number) => 1 - Math.cos(t * Math.PI / 2);

      case 'easeOutSine':
        return (t: number) => Math.sin(t * Math.PI / 2);

      case 'easeInOutSine':
        return (t: number) => (1 - Math.cos(Math.PI * t)) / 2;

      case 'easeInExpo':
        return (t: number) => t === 0 ? 0 : Math.pow(2, 10 * (t - 1));

      case 'easeOutExpo':
        return (t: number) => t === 1 ? 1 : 1 - Math.pow(2, -10 * t);

      case 'easeInOutExpo':
        return (t: number) => {
          if (t === 0) return 0;
          if (t === 1) return 1;
          if (t < 0.5) return Math.pow(2, 10 * (2 * t - 1)) / 2;
          return (2 - Math.pow(2, -10 * (2 * t - 1))) / 2;
        };

      case 'easeInCirc':
        return (t: number) => 1 - Math.sqrt(1 - t * t);

      case 'easeOutCirc':
        return (t: number) => Math.sqrt(1 - (--t) * t);

      case 'easeInOutCirc':
        return (t: number) => {
          t *= 2;
          if (t < 1) return (1 - Math.sqrt(1 - t * t)) / 2;
          t -= 2;
          return (Math.sqrt(1 - t * t) + 1) / 2;
        };

      case 'easeInElastic':
        return (t: number) => {
          if (t === 0) return 0;
          if (t === 1) return 1;
          return -Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
        };

      case 'easeOutElastic':
        return (t: number) => {
          if (t === 0) return 0;
          if (t === 1) return 1;
          return Math.pow(2, -10 * t) * Math.sin((t - 0.1) * 5 * Math.PI) + 1;
        };

      case 'easeInOutElastic':
        return (t: number) => {
          if (t === 0) return 0;
          if (t === 1) return 1;
          t *= 2;
          if (t < 1) return -0.5 * Math.pow(2, 10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI);
          return 0.5 * Math.pow(2, -10 * (t - 1)) * Math.sin((t - 1.1) * 5 * Math.PI) + 1;
        };

      case 'easeInBack':
        return (t: number) => {
          const s = 1.70158;
          return t * t * ((s + 1) * t - s);
        };

      case 'easeOutBack':
        return (t: number) => {
          const s = 1.70158;
          return (t = t - 1) * t * ((s + 1) * t + s) + 1;
        };

      case 'easeInOutBack':
        return (t: number) => {
          const s = 1.70158 * 1.525;
          if ((t *= 2) < 1) return 0.5 * (t * t * ((s + 1) * t - s));
          return 0.5 * ((t -= 2) * t * ((s + 1) * t + s) + 2);
        };

      case 'easeInBounce':
        return (t: number) => {
          return 1 - this.createPresetCurve('easeOutBounce')(1 - t);
        };

      case 'easeOutBounce':
        return (t: number) => {
          if (t < 1 / 2.75) {
            return 7.5625 * t * t;
          } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
          } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
          } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
          }
        };

      case 'easeInOutBounce':
        return (t: number) => {
          if (t < 0.5) return this.createPresetCurve('easeInBounce')(t * 2) * 0.5;
          return this.createPresetCurve('easeOutBounce')(t * 2 - 1) * 0.5 + 0.5;
        };

      default:
        // 默认返回线性曲线
        return (t: number) => t;
    }
  }

  /**
   * 开始混合
   * @param blendTime 混合时间（秒）
   */
  private startBlending(blendTime: number): void {
    this.isBlending = true;
    this.blendTime = 0;
    this.targetBlendTime = blendTime;

    // 发出混合开始事件
    this.eventEmitter.emit(BlendEventType.BLEND_START, {
      blendTime: this.targetBlendTime
    });
  }

  /**
   * 更新混合器
   * @param deltaTime 时间增量（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled) return;

    // 开始性能监控
    const performanceMonitor = BlendPerformanceMonitor.getInstance();
    const updateOpId = performanceMonitor.startOperation('update', {
      layerCount: this.layers.length,
      deltaTime
    });

    // 如果正在混合
    if (this.isBlending) {
      // 开始混合性能监控
      const blendOpId = performanceMonitor.startOperation('blend', {
        blendTime: this.blendTime,
        targetBlendTime: this.targetBlendTime
      });

      // 更新混合时间
      this.blendTime += deltaTime;

      // 计算混合因子
      let t = this.blendTime / this.targetBlendTime;
      t = Math.min(t, 1.0);

      // 应用混合曲线
      t = this.applyBlendCurve(t);

      // 发出混合更新事件
      this.eventEmitter.emit(BlendEventType.BLEND_UPDATE, {
        blendTime: this.blendTime,
        blendFactor: t
      });

      // 如果混合完成
      if (this.blendTime >= this.targetBlendTime) {
        this.isBlending = false;

        // 发出混合结束事件
        this.eventEmitter.emit(BlendEventType.BLEND_END, {
          blendTime: this.blendTime
        });
      }

      // 结束混合性能监控
      performanceMonitor.endOperation(blendOpId, {
        blendFactor: t,
        isComplete: !this.isBlending
      });
    }

    // 应用混合层
    const applyLayersOpId = performanceMonitor.startOperation('applyLayers', {
      layerCount: this.layers.length
    });
    this.applyLayers();
    performanceMonitor.endOperation(applyLayersOpId);

    // 结束性能监控
    performanceMonitor.endOperation(updateOpId, {
      useCache: this.cacheEnabled,
      useObjectPool: this.objectPoolEnabled,
      useBatchProcessing: this.batchProcessingEnabled
    });
  }

  /**
   * 应用混合曲线
   * @param t 混合因子（0-1）
   * @returns 应用曲线后的混合因子
   */
  private applyBlendCurve(t: number): number {
    // 确保t在0-1范围内
    t = Math.max(0, Math.min(1, t));

    // 如果启用缓存，尝试从缓存中获取结果
    if (this.cacheEnabled) {
      const cacheKey = this.blendCurveType.toString();
      const tKey = Math.round(t * this.cacheResolution) / this.cacheResolution;

      // 检查缓存
      if (!this.curveCache.has(cacheKey)) {
        this.curveCache.set(cacheKey, new Map<number, number>());
      }

      const curveMap = this.curveCache.get(cacheKey)!;

      if (curveMap.has(tKey)) {
        return curveMap.get(tKey)!;
      }

      // 计算结果并缓存
      const result = this.calculateCurveValue(t);
      curveMap.set(tKey, result);
      return result;
    }

    // 如果未启用缓存，直接计算
    return this.calculateCurveValue(t);
  }

  /**
   * 计算曲线值
   * @param t 混合因子（0-1）
   * @returns 曲线值
   */
  private calculateCurveValue(t: number): number {
    switch (this.blendCurveType) {
      case BlendCurveType.LINEAR:
        return t;

      case BlendCurveType.EASE_IN:
        return t * t;

      case BlendCurveType.EASE_OUT:
        return t * (2 - t);

      case BlendCurveType.EASE_IN_OUT:
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;

      case BlendCurveType.ELASTIC:
        // 弹性曲线
        const p = 0.3;
        return Math.pow(2, -10 * t) * Math.sin((t - p / 4) * (2 * Math.PI) / p) + 1;

      case BlendCurveType.BOUNCE:
        // 弹跳曲线
        if (t < 1 / 2.75) {
          return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
          const t2 = t - 1.5 / 2.75;
          return 7.5625 * t2 * t2 + 0.75;
        } else if (t < 2.5 / 2.75) {
          const t2 = t - 2.25 / 2.75;
          return 7.5625 * t2 * t2 + 0.9375;
        } else {
          const t2 = t - 2.625 / 2.75;
          return 7.5625 * t2 * t2 + 0.984375;
        }

      case BlendCurveType.SINE:
        // 正弦曲线
        return Math.sin(t * Math.PI / 2);

      case BlendCurveType.EXPONENTIAL:
        // 指数曲线
        return t === 0 ? 0 : Math.pow(2, 10 * (t - 1));

      case BlendCurveType.CIRCULAR:
        // 圆形曲线
        return 1 - Math.sqrt(1 - t * t);

      case BlendCurveType.QUADRATIC:
        // 二次方曲线
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;

      case BlendCurveType.CUBIC:
        // 三次方曲线
        return t * t * t;

      case BlendCurveType.QUARTIC:
        // 四次方曲线
        return t * t * t * t;

      case BlendCurveType.QUINTIC:
        // 五次方曲线
        return t * t * t * t * t;

      case BlendCurveType.CUSTOM:
        // 自定义曲线
        return this.customBlendCurve ? this.customBlendCurve(t) : t;

      default:
        return t;
    }
  }

  /**
   * 应用混合层
   */
  private applyLayers(): void {
    // 如果没有混合层，则不做任何操作
    if (this.layers.length === 0) return;

    // 获取动画控制器的混合器
    const mixer = this.animator.getMixer();
    if (!mixer) return;

    // 开始性能监控
    const performanceMonitor = BlendPerformanceMonitor.getInstance();
    const layersOpId = performanceMonitor.startOperation('applyLayersInternal', {
      layerCount: this.layers.length
    });

    // 应用每个混合层
    for (let i = 0; i < this.layers.length; i++) {
      const layer = this.layers[i];
      const layerOpId = performanceMonitor.startOperation('applyLayer', {
        layerIndex: i,
        clipName: layer.clipName,
        blendMode: layer.blendMode,
        weight: layer.weight,
        hasMask: layer.mask && layer.mask.length > 0
      });

      const action = this.animator.getAction(layer.clipName);

      if (action) {
        // 设置权重
        action.weight = layer.weight;

        // 设置时间缩放
        action.timeScale = layer.timeScale;

        // 设置混合模式
        const blendModeOpId = performanceMonitor.startOperation('setBlendMode', {
          blendMode: layer.blendMode
        });

        switch (layer.blendMode) {
          case BlendMode.OVERRIDE:
            // 覆盖模式：完全替换之前的动画
            // 设置权重为1，其他动作权重为0
            action.weight = layer.weight;
            break;
          case BlendMode.ADDITIVE:
            // 叠加模式：在之前的动画基础上叠加
            // 保持当前权重，允许多个动作同时播放
            action.weight = layer.weight;
            break;
          case BlendMode.MULTIPLY:
            // 乘法混合：将当前动画与之前的动画相乘
            // 这需要在动画数据层面处理，这里只设置权重
            action.weight = layer.weight;
            break;
          case BlendMode.DIFFERENCE:
            // 差值混合：计算当前动画与之前动画的差值
            // 这需要在动画数据层面处理，这里只设置权重
            action.weight = layer.weight;
            break;
          case BlendMode.AVERAGE:
            // 平均混合：计算当前动画与之前动画的平均值
            // 这需要在动画数据层面处理，这里只设置权重
            action.weight = layer.weight * 0.5; // 平均权重
            break;
          case BlendMode.MAX:
            // 最大值混合：取当前动画与之前动画中的最大值
            // 这需要在动画数据层面处理，这里只设置权重
            action.weight = layer.weight;
            break;
          case BlendMode.MIN:
            // 最小值混合：取当前动画与之前动画中的最小值
            // 这需要在动画数据层面处理，这里只设置权重
            action.weight = layer.weight;
            break;
          case BlendMode.CROSS_FADE:
            // 实现交叉淡入淡出
            action.weight = layer.weight;

            // 如果有前一个动作，设置交叉淡入淡出
            const prevLayerIndex = i - 1;
            if (prevLayerIndex >= 0) {
              const prevLayer = this.layers[prevLayerIndex];
              const prevAction = this.animator.getAction(prevLayer.clipName);
              if (prevAction) {
                // 设置交叉淡入淡出
                action.crossFadeTo(prevAction, layer.crossFadeTime || 1.0, true);
              }
            }
            break;
          case BlendMode.BLEND_TREE:
            // 实现混合树
            action.weight = layer.weight;

            // 如果有混合树参数，应用混合树
            if (layer.blendTreeParams) {
              this.applyBlendTree(action, layer.blendTreeParams);
            }
            break;
          case BlendMode.WEIGHTED:
            // 实现加权混合
            action.weight = layer.weight;

            // 如果有权重参数，应用权重
            if (layer.weightParams) {
              this.applyWeightedBlend(action, layer.weightParams);
            }
            break;
          case BlendMode.LAYERED:
            // 实现分层混合
            action.weight = layer.weight;

            // 如果有层次参数，应用层次
            if (layer.layerParams) {
              this.applyLayeredBlend(action, layer.layerParams);
            }
            break;
          case BlendMode.SEQUENTIAL:
            // 实现序列混合
            action.weight = layer.weight;

            // 如果有序列参数，应用序列
            if (layer.sequenceParams) {
              this.applySequentialBlend(action, layer.sequenceParams);
            }
            break;
          default:
            action.weight = layer.weight;
            break;
        }

        // 结束混合模式性能监控
        performanceMonitor.endOperation(blendModeOpId);

        // 设置遮罩
        if (layer.mask && layer.mask.length > 0) {
          // 开始遮罩性能监控
          const maskOpId = performanceMonitor.startOperation('applyMask', {
            clipName: layer.clipName,
            maskCount: layer.mask.length
          });

          // 应用遮罩
          this.applyMaskToAction(action, layer.mask);

          // 结束遮罩性能监控
          performanceMonitor.endOperation(maskOpId);
        }

        // 播放动作
        if (!action.isRunning()) {
          action.play();
        }
      }

      // 结束层性能监控
      performanceMonitor.endOperation(layerOpId);
    }

    // 结束总体性能监控
    performanceMonitor.endOperation(layersOpId);
  }

  /**
   * 应用遮罩到动作
   * @param action 动作
   * @param maskBones 遮罩骨骼列表
   */
  private applyMaskToAction(action: THREE.AnimationAction, maskBones: string[]): void {
    // 开始性能监控
    const performanceMonitor = BlendPerformanceMonitor.getInstance();
    const maskOpId = performanceMonitor.startOperation('applyMaskToAction', {
      clipName: action.getClip().name,
      maskBonesCount: maskBones.length
    });

    // 获取动作的动画片段
    const clip = action.getClip();

    // 创建遮罩
    const createMaskOpId = performanceMonitor.startOperation('createMask', {
      clipName: clip.name
    });

    const mask = new AnimationMask(`${clip.name}_mask`);

    // 添加遮罩规则
    for (const bone of maskBones) {
      mask.addRule(bone, 1.0, false);
    }

    performanceMonitor.endOperation(createMaskOpId);

    // 应用遮罩到动画片段
    const applyMaskOpId = performanceMonitor.startOperation('applyMaskToClip', {
      clipName: clip.name
    });

    const maskedClip = mask.applyToClip(clip);

    performanceMonitor.endOperation(applyMaskOpId);

    // 更新动作的动画片段
    // 注意：Three.js的AnimationAction没有setClip方法，需要重新创建动作
    const setClipOpId = performanceMonitor.startOperation('recreateAction', {
      clipName: maskedClip.name
    });

    // 停止当前动作
    action.stop();

    // 从混合器中移除当前动作
    const mixer = this.animator.getMixer();
    if (mixer) {
      // 创建新的动作
      const newAction = mixer.clipAction(maskedClip);
      newAction.weight = action.weight;
      newAction.timeScale = action.timeScale;

      // 更新动画器中的动作映射
      const clipName = action.getClip().name;
      this.animator.updateAction(clipName, newAction);
    }

    performanceMonitor.endOperation(setClipOpId);

    // 结束性能监控
    performanceMonitor.endOperation(maskOpId);
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public addEventListener(type: BlendEventType, callback: (data: any) => void): void {
    this.eventEmitter.on(type, callback);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param callback 回调函数
   */
  public removeEventListener(type: BlendEventType, callback: (data: any) => void): void {
    this.eventEmitter.off(type, callback);
  }

  /**
   * 销毁混合器
   */
  public dispose(): void {
    this.clearLayers();
    this.masks.clear();
    this.subClips.clear();

    // 清空缓存
    this.clearCache();

    // 清空对象池
    this.clearObjectPool();

    // 销毁物理集成
    if (this.physicsIntegration) {
      this.physicsIntegration.destroy();
      this.physicsIntegration = undefined;
    }

    // 销毁输入集成
    if (this.inputIntegration) {
      this.inputIntegration.destroy();
      this.inputIntegration = undefined;
    }

    this.eventEmitter.removeAllListeners();
  }

  /**
   * 设置缓存配置
   * @param enabled 是否启用缓存
   * @param resolution 缓存精度
   */
  public setCacheConfig(enabled: boolean, resolution: number = 1000): void {
    this.cacheEnabled = enabled;
    this.cacheResolution = resolution;

    // 如果禁用缓存，清空缓存
    if (!enabled) {
      this.clearCache();
    }
  }

  /**
   * 清空缓存
   */
  public clearCache(): void {
    this.curveCache.clear();
    this.weightCache.clear();
  }

  /**
   * 设置对象池配置
   * @param enabled 是否启用对象池
   */
  public setObjectPoolConfig(enabled: boolean): void {
    this.objectPoolEnabled = enabled;

    // 如果禁用对象池，清空对象池
    if (!enabled) {
      this.clearObjectPool();
    }
  }

  /**
   * 清空对象池
   */
  public clearObjectPool(): void {
    this.objectPool.clear();
  }

  /**
   * 从对象池获取对象
   * @param type 对象类型
   * @returns 对象
   */
  private getFromPool<T>(type: string): T | null {
    if (!this.objectPoolEnabled) return null;

    if (!this.objectPool.has(type)) {
      this.objectPool.set(type, []);
      return null;
    }

    const pool = this.objectPool.get(type)!;
    if (pool.length === 0) return null;

    return pool.pop() as T;
  }

  /**
   * 将对象放回对象池
   * @param type 对象类型
   * @param obj 对象
   */
  private returnToPool<T>(type: string, obj: T): void {
    if (!this.objectPoolEnabled) return;

    if (!this.objectPool.has(type)) {
      this.objectPool.set(type, []);
    }

    const pool = this.objectPool.get(type)!;
    pool.push(obj);
  }

  /**
   * 设置批处理配置
   * @param enabled 是否启用批处理
   */
  public setBatchProcessingConfig(enabled: boolean): void {
    this.batchProcessingEnabled = enabled;
  }

  /**
   * 获取性能统计信息
   * @returns 性能统计信息
   */
  public getPerformanceStats(): any {
    // 获取性能监控器的统计信息
    const performanceMonitor = BlendPerformanceMonitor.getInstance();
    const monitorStats = performanceMonitor.getStats();

    // 合并基本统计信息和性能监控统计信息
    return {
      // 基本配置信息
      cacheEnabled: this.cacheEnabled,
      cacheSize: {
        curves: Array.from(this.curveCache.entries()).reduce((total, [key, map]) => total + map.size, 0),
        weights: this.weightCache.size
      },
      objectPoolEnabled: this.objectPoolEnabled,
      objectPoolSize: Array.from(this.objectPool.entries()).reduce((total, [key, arr]) => total + arr.length, 0),
      batchProcessingEnabled: this.batchProcessingEnabled,
      layersCount: this.layers.length,
      masksCount: this.masks.size,
      subClipsCount: this.subClips.size,

      // 性能监控统计信息
      performance: monitorStats
    };
  }

  /**
   * 应用混合树
   * @param action 动作
   * @param params 混合树参数
   */
  private applyBlendTree(action: THREE.AnimationAction, params: BlendTreeParams): void {
    if (!params || !params.nodes || params.nodes.length === 0) return;

    // 根据混合类型计算权重
    switch (params.blendType) {
      case '1D':
        this.applyBlendTree1D(action, params);
        break;
      case '2D':
        this.applyBlendTree2D(action, params);
        break;
      case 'Direct':
        this.applyBlendTreeDirect(action, params);
        break;
    }
  }

  /**
   * 应用1D混合树
   * @param action 动作
   * @param params 混合树参数
   */
  private applyBlendTree1D(action: THREE.AnimationAction, params: BlendTreeParams): void {
    const { paramValue, nodes } = params;

    // 排序节点
    const sortedNodes = [...nodes].sort((a, b) => {
      const thresholdA = a.threshold as number;
      const thresholdB = b.threshold as number;
      return thresholdA - thresholdB;
    });

    // 找到参数值所在的区间
    let leftNode: BlendTreeNode | null = null;
    let rightNode: BlendTreeNode | null = null;

    for (let i = 0; i < sortedNodes.length - 1; i++) {
      const currentNode = sortedNodes[i];
      const nextNode = sortedNodes[i + 1];

      const currentThreshold = currentNode.threshold as number;
      const nextThreshold = nextNode.threshold as number;

      if (paramValue >= currentThreshold && paramValue <= nextThreshold) {
        leftNode = currentNode;
        rightNode = nextNode;
        break;
      }
    }

    // 如果参数值小于最小阈值
    if (paramValue <= (sortedNodes[0].threshold as number)) {
      leftNode = null;
      rightNode = sortedNodes[0];
    }

    // 如果参数值大于最大阈值
    if (paramValue >= (sortedNodes[sortedNodes.length - 1].threshold as number)) {
      leftNode = sortedNodes[sortedNodes.length - 1];
      rightNode = null;
    }

    // 计算混合权重
    if (leftNode && rightNode) {
      const leftThreshold = leftNode.threshold as number;
      const rightThreshold = rightNode.threshold as number;
      const t = (paramValue - leftThreshold) / (rightThreshold - leftThreshold);

      // 设置权重
      leftNode.weight = 1 - t;
      rightNode.weight = t;

      // 其他节点权重为0
      for (const node of nodes) {
        if (node !== leftNode && node !== rightNode) {
          node.weight = 0;
        }
      }
    } else if (leftNode) {
      // 只有左节点
      leftNode.weight = 1;

      // 其他节点权重为0
      for (const node of nodes) {
        if (node !== leftNode) {
          node.weight = 0;
        }
      }
    } else if (rightNode) {
      // 只有右节点
      rightNode.weight = 1;

      // 其他节点权重为0
      for (const node of nodes) {
        if (node !== rightNode) {
          node.weight = 0;
        }
      }
    }

    // 应用权重
    for (const node of nodes) {
      const nodeAction = this.animator.getAction(node.clipName);
      if (nodeAction) {
        nodeAction.weight = node.weight;
        nodeAction.play();
      }
    }
  }

  /**
   * 应用2D混合树
   * @param action 动作
   * @param params 混合树参数
   */
  private applyBlendTree2D(action: THREE.AnimationAction, params: BlendTreeParams): void {
    const { paramValue, nodes } = params;

    // 参数值应该是一个2D坐标
    if (typeof paramValue !== 'object') return;

    const paramValueObj = paramValue as { x: number; y: number };

    // 计算三角形
    const triangles = this.calculateTriangles(nodes);

    // 找到包含参数值的三角形
    let containingTriangle: [BlendTreeNode, BlendTreeNode, BlendTreeNode] | null = null;

    for (const triangle of triangles) {
      if (this.isPointInTriangle(
        paramValueObj.x, paramValueObj.y,
        (triangle[0].threshold as { x: number; y: number }).x,
        (triangle[0].threshold as { x: number; y: number }).y,
        (triangle[1].threshold as { x: number; y: number }).x,
        (triangle[1].threshold as { x: number; y: number }).y,
        (triangle[2].threshold as { x: number; y: number }).x,
        (triangle[2].threshold as { x: number; y: number }).y
      )) {
        containingTriangle = triangle;
        break;
      }
    }

    // 如果找到包含参数值的三角形
    if (containingTriangle) {
      // 计算重心坐标
      const weights = this.calculateBarycentricWeights(
        paramValueObj.x, paramValueObj.y,
        (containingTriangle[0].threshold as { x: number; y: number }).x,
        (containingTriangle[0].threshold as { x: number; y: number }).y,
        (containingTriangle[1].threshold as { x: number; y: number }).x,
        (containingTriangle[1].threshold as { x: number; y: number }).y,
        (containingTriangle[2].threshold as { x: number; y: number }).x,
        (containingTriangle[2].threshold as { x: number; y: number }).y
      );

      // 设置权重
      containingTriangle[0].weight = weights[0];
      containingTriangle[1].weight = weights[1];
      containingTriangle[2].weight = weights[2];

      // 其他节点权重为0
      for (const node of nodes) {
        if (node !== containingTriangle[0] && node !== containingTriangle[1] && node !== containingTriangle[2]) {
          node.weight = 0;
        }
      }
    } else {
      // 如果没有找到包含参数值的三角形，使用最近的节点
      let minDistance = Infinity;
      let closestNode: BlendTreeNode | null = null;

      for (const node of nodes) {
        const threshold = node.threshold as { x: number; y: number };
        const distance = Math.sqrt(
          Math.pow(paramValueObj.x - threshold.x, 2) +
          Math.pow(paramValueObj.y - threshold.y, 2)
        );

        if (distance < minDistance) {
          minDistance = distance;
          closestNode = node;
        }
      }

      // 设置权重
      if (closestNode) {
        closestNode.weight = 1;

        // 其他节点权重为0
        for (const node of nodes) {
          if (node !== closestNode) {
            node.weight = 0;
          }
        }
      }
    }

    // 应用权重
    for (const node of nodes) {
      const nodeAction = this.animator.getAction(node.clipName);
      if (nodeAction) {
        nodeAction.weight = node.weight;
        nodeAction.play();
      }
    }
  }

  /**
   * 应用直接混合树
   * @param action 动作
   * @param params 混合树参数
   */
  private applyBlendTreeDirect(action: THREE.AnimationAction, params: BlendTreeParams): void {
    const { nodes } = params;

    // 应用权重
    for (const node of nodes) {
      const nodeAction = this.animator.getAction(node.clipName);
      if (nodeAction) {
        nodeAction.weight = node.weight;
        nodeAction.play();
      }
    }
  }

  /**
   * 计算三角形
   * @param nodes 节点列表
   * @returns 三角形列表
   */
  private calculateTriangles(nodes: BlendTreeNode[]): [BlendTreeNode, BlendTreeNode, BlendTreeNode][] {
    if (nodes.length < 3) return [];

    const triangles: [BlendTreeNode, BlendTreeNode, BlendTreeNode][] = [];

    // 简单的三角剖分算法
    for (let i = 0; i < nodes.length - 2; i++) {
      for (let j = i + 1; j < nodes.length - 1; j++) {
        for (let k = j + 1; k < nodes.length; k++) {
          const a = nodes[i];
          const b = nodes[j];
          const c = nodes[k];

          // 检查三角形是否有效
          const thresholdA = a.threshold as { x: number; y: number };
          const thresholdB = b.threshold as { x: number; y: number };
          const thresholdC = c.threshold as { x: number; y: number };

          // 计算三角形面积
          const area = Math.abs(
            (thresholdA.x * (thresholdB.y - thresholdC.y) +
             thresholdB.x * (thresholdC.y - thresholdA.y) +
             thresholdC.x * (thresholdA.y - thresholdB.y)) / 2
          );

          // 如果面积接近于0，则三点共线
          if (area > 0.0001) {
            triangles.push([a, b, c]);
          }
        }
      }
    }

    return triangles;
  }

  /**
   * 判断点是否在三角形内
   * @param px 点的x坐标
   * @param py 点的y坐标
   * @param ax 三角形顶点A的x坐标
   * @param ay 三角形顶点A的y坐标
   * @param bx 三角形顶点B的x坐标
   * @param by 三角形顶点B的y坐标
   * @param cx 三角形顶点C的x坐标
   * @param cy 三角形顶点C的y坐标
   * @returns 是否在三角形内
   */
  private isPointInTriangle(
    px: number, py: number,
    ax: number, ay: number,
    bx: number, by: number,
    cx: number, cy: number
  ): boolean {
    const v0x = cx - ax;
    const v0y = cy - ay;
    const v1x = bx - ax;
    const v1y = by - ay;
    const v2x = px - ax;
    const v2y = py - ay;

    const dot00 = v0x * v0x + v0y * v0y;
    const dot01 = v0x * v1x + v0y * v1y;
    const dot02 = v0x * v2x + v0y * v2y;
    const dot11 = v1x * v1x + v1y * v1y;
    const dot12 = v1x * v2x + v1y * v2y;

    const invDenom = 1 / (dot00 * dot11 - dot01 * dot01);
    const u = (dot11 * dot02 - dot01 * dot12) * invDenom;
    const v = (dot00 * dot12 - dot01 * dot02) * invDenom;

    return u >= 0 && v >= 0 && u + v <= 1;
  }

  /**
   * 计算重心坐标
   * @param px 点的x坐标
   * @param py 点的y坐标
   * @param ax 三角形顶点A的x坐标
   * @param ay 三角形顶点A的y坐标
   * @param bx 三角形顶点B的x坐标
   * @param by 三角形顶点B的y坐标
   * @param cx 三角形顶点C的x坐标
   * @param cy 三角形顶点C的y坐标
   * @returns 重心坐标
   */
  private calculateBarycentricWeights(
    px: number, py: number,
    ax: number, ay: number,
    bx: number, by: number,
    cx: number, cy: number
  ): [number, number, number] {
    const v0x = bx - ax;
    const v0y = by - ay;
    const v1x = cx - ax;
    const v1y = cy - ay;
    const v2x = px - ax;
    const v2y = py - ay;

    const d00 = v0x * v0x + v0y * v0y;
    const d01 = v0x * v1x + v0y * v1y;
    const d11 = v1x * v1x + v1y * v1y;
    const d20 = v2x * v0x + v2y * v0y;
    const d21 = v2x * v1x + v2y * v1y;

    const denom = d00 * d11 - d01 * d01;

    const v = (d11 * d20 - d01 * d21) / denom;
    const w = (d00 * d21 - d01 * d20) / denom;
    const u = 1.0 - v - w;

    return [u, v, w];
  }

  /**
   * 应用加权混合
   * @param action 动作
   * @param params 加权混合参数
   */
  private applyWeightedBlend(action: THREE.AnimationAction, params: WeightedBlendParams): void {
    const { weights, normalize } = params;

    // 如果需要归一化权重
    if (normalize) {
      // 计算权重总和
      let totalWeight = 0;
      for (const clipName in weights) {
        totalWeight += weights[clipName];
      }

      // 归一化权重
      if (totalWeight > 0) {
        for (const clipName in weights) {
          weights[clipName] /= totalWeight;
        }
      }
    }

    // 应用权重
    for (const clipName in weights) {
      const weight = weights[clipName];
      const clipAction = this.animator.getAction(clipName);

      if (clipAction) {
        clipAction.weight = weight;
        clipAction.play();
      }
    }
  }

  /**
   * 应用分层混合
   * @param action 动作
   * @param params 分层混合参数
   */
  private applyLayeredBlend(action: THREE.AnimationAction, params: LayeredBlendParams): void {
    const { layers } = params;

    // 按层次索引排序
    const sortedLayers = [...layers].sort((a, b) => a.layerIndex - b.layerIndex);

    // 应用层次
    for (const layer of sortedLayers) {
      const { clipName, weight, mask } = layer;
      const layerAction = this.animator.getAction(clipName);

      if (layerAction) {
        // 设置权重
        layerAction.weight = weight;

        // 设置遮罩
        if (mask && mask.length > 0) {
          this.applyMaskToAction(layerAction, mask);
        }

        // 播放动作
        layerAction.play();
      }
    }
  }

  /**
   * 应用序列混合
   * @param action 动作
   * @param params 序列混合参数
   */
  private applySequentialBlend(action: THREE.AnimationAction, params: SequentialBlendParams): void {
    const { sequence, currentIndex, loop } = params;

    // 如果序列为空，直接返回
    if (sequence.length === 0) return;

    // 获取当前片段
    const currentClip = sequence[currentIndex];

    // 播放当前片段
    const currentAction = this.animator.getAction(currentClip.clipName);
    if (currentAction) {
      currentAction.weight = 1;
      currentAction.play();

      // 设置动作结束回调
      // 注意：Three.js的AnimationAction没有onComplete属性，需要使用混合器的事件
      const mixer = this.animator.getMixer();
      if (mixer) {
        const onFinished = (event: any) => {
          // 检查是否是当前动作结束
          if (event.action !== currentAction) return;

          // 计算下一个索引
          let nextIndex = currentIndex + 1;

          // 如果到达序列末尾
          if (nextIndex >= sequence.length) {
            // 如果循环，则回到开始
            if (loop) {
              nextIndex = 0;
            } else {
              // 否则停止
              mixer.removeEventListener('finished', onFinished);
              return;
            }
          }

          // 获取下一个片段
          const nextClip = sequence[nextIndex];

          // 播放下一个片段
          const nextAction = this.animator.getAction(nextClip.clipName);
          if (nextAction) {
            // 如果有过渡时间，则交叉淡入淡出
            if (nextClip.transitionTime > 0) {
              currentAction.crossFadeTo(nextAction, nextClip.transitionTime, true);
            } else {
              // 否则直接播放
              currentAction.stop();
              nextAction.play();
            }

            // 更新当前索引
            params.currentIndex = nextIndex;
          }
        };

        mixer.addEventListener('finished', onFinished);
      }
    }
  }

  /**
   * 获取动画控制器
   * @returns 动画控制器
   */
  public getAnimator(): Animator {
    return this.animator;
  }

  /**
   * 创建遮罩
   * @param name 遮罩名称
   * @param type 遮罩类型
   * @param bones 骨骼列表
   * @param weightType 权重类型
   * @returns 遮罩
   */
  public createMask(
    name: string,
    bones: string[] = []
  ): AnimationMask {
    // 创建遮罩
    const mask = new AnimationMask(name);

    // 添加骨骼规则
    for (const bone of bones) {
      mask.addRule(bone, 1.0, false);
    }

    // 添加到映射
    this.masks.set(name, mask);

    return mask;
  }

  /**
   * 获取遮罩
   * @param name 遮罩名称
   * @returns 遮罩
   */
  public getMask(name: string): AnimationMask | undefined {
    return this.masks.get(name);
  }

  /**
   * 移除遮罩
   * @param name 遮罩名称
   * @returns 是否成功移除
   */
  public removeMask(name: string): boolean {
    return this.masks.delete(name);
  }

  /**
   * 应用遮罩到动画片段
   * @param clipName 动画片段名称
   * @param maskName 遮罩名称
   * @returns 是否成功应用
   */
  public applyMaskToClip(clipName: string, maskName: string): boolean {
    // 获取动画片段
    const clip = this.animator.getClip(clipName);
    if (!clip) {
      console.warn(`动画片段 "${clipName}" 不存在`);
      return false;
    }

    // 获取遮罩
    const mask = this.masks.get(maskName);
    if (!mask) {
      console.warn(`遮罩 "${maskName}" 不存在`);
      return false;
    }

    // 应用遮罩
    const threeClip = AnimationClipAdapter.ensureThreeClip(clip);
    const maskedClip = mask.applyToClip(threeClip);

    // 更新动画片段
    const customMaskedClip = AnimationClipAdapter.ensureCustomClip(maskedClip);
    this.animator.updateClip(clipName, customMaskedClip);

    // 触发遮罩应用事件
    this.eventEmitter.emit(BlendEventType.MASK_APPLIED, {
      clipName,
      maskName
    });

    return true;
  }

  /**
   * 创建上半身遮罩
   * @returns 遮罩
   */
  public createUpperBodyMask(): AnimationMask {
    const mask = AnimationMask.createUpperBodyMask();
    this.masks.set(mask.getName(), mask);
    return mask;
  }

  /**
   * 创建下半身遮罩
   * @returns 遮罩
   */
  public createLowerBodyMask(): AnimationMask {
    const mask = AnimationMask.createLowerBodyMask();
    this.masks.set(mask.getName(), mask);
    return mask;
  }

  /**
   * 创建左手遮罩
   * @returns 遮罩
   */
  public createLeftHandMask(): AnimationMask {
    const mask = AnimationMask.createLeftHandMask();
    this.masks.set(mask.getName(), mask);
    return mask;
  }

  /**
   * 创建右手遮罩
   * @returns 遮罩
   */
  public createRightHandMask(): AnimationMask {
    const mask = AnimationMask.createRightHandMask();
    this.masks.set(mask.getName(), mask);
    return mask;
  }

  /**
   * 创建子片段
   * @param name 子片段名称
   * @param sourceName 源片段名称
   * @param startTime 开始时间（秒）
   * @param endTime 结束时间（秒）
   * @param loop 是否循环
   * @returns 子片段
   */
  public createSubClip(
    name: string,
    sourceName: string,
    startTime: number,
    endTime: number,
    loop: boolean = true
  ): SubClip {
    // 创建子片段
    const subClip = new SubClip({
      name,
      sourceName,
      startTime,
      endTime,
      loop
    });

    // 添加到映射
    this.subClips.set(name, subClip);

    // 获取源片段
    const sourceClip = this.animator.getClip(sourceName);
    if (sourceClip) {
      // 创建子片段
      const threeSourceClip = AnimationClipAdapter.ensureThreeClip(sourceClip);
      const clip = subClip.createFromClip(threeSourceClip);

      // 添加到动画控制器
      const customClip = AnimationClipAdapter.ensureCustomClip(clip);
      this.animator.addClip(customClip);

      // 触发子片段创建事件
      this.eventEmitter.emit(BlendEventType.SUBCLIP_CREATED, {
        name,
        sourceName,
        startTime,
        endTime,
        loop
      });
    } else {
      console.warn(`源片段 "${sourceName}" 不存在`);
    }

    return subClip;
  }

  /**
   * 获取子片段
   * @param name 子片段名称
   * @returns 子片段
   */
  public getSubClip(name: string): SubClip | undefined {
    return this.subClips.get(name);
  }

  /**
   * 移除子片段
   * @param name 子片段名称
   * @returns 是否成功移除
   */
  public removeSubClip(name: string): boolean {
    // 移除子片段
    const removed = this.subClips.delete(name);

    // 移除动画片段
    if (removed) {
      this.animator.removeClip(name);
    }

    return removed;
  }

  /**
   * 创建动作子片段
   * @param sourceClipName 源片段名称
   * @param actionName 动作名称
   * @param startTime 开始时间（秒）
   * @param endTime 结束时间（秒）
   * @param loop 是否循环
   * @returns 子片段名称
   */
  public createActionSubClip(
    sourceClipName: string,
    actionName: string,
    startTime: number,
    endTime: number,
    loop: boolean = true
  ): string {
    const name = `${sourceClipName}_${actionName}`;
    this.createSubClip(name, sourceClipName, startTime, endTime, loop);
    return name;
  }

  /**
   * 创建循环子片段
   * @param sourceClipName 源片段名称
   * @param loopStartTime 循环开始时间（秒）
   * @param loopEndTime 循环结束时间（秒）
   * @returns 子片段名称
   */
  public createLoopSubClip(
    sourceClipName: string,
    loopStartTime: number,
    loopEndTime: number
  ): string {
    const name = `${sourceClipName}_loop`;
    this.createSubClip(name, sourceClipName, loopStartTime, loopEndTime, true);
    return name;
  }

  /**
   * 创建入场子片段
   * @param sourceClipName 源片段名称
   * @param entryEndTime 入场结束时间（秒）
   * @returns 子片段名称
   */
  public createEntrySubClip(
    sourceClipName: string,
    entryEndTime: number
  ): string {
    const name = `${sourceClipName}_entry`;
    this.createSubClip(name, sourceClipName, 0, entryEndTime, false);
    return name;
  }

  /**
   * 创建退场子片段
   * @param sourceClipName 源片段名称
   * @param exitStartTime 退场开始时间（秒）
   * @returns 子片段名称
   */
  public createExitSubClip(
    sourceClipName: string,
    exitStartTime: number
  ): string {
    // 获取源片段
    const sourceClip = this.animator.getClip(sourceClipName);
    if (!sourceClip) {
      console.warn(`源片段 "${sourceClipName}" 不存在`);
      return '';
    }

    const name = `${sourceClipName}_exit`;
    this.createSubClip(name, sourceClipName, exitStartTime, sourceClip.duration, false);
    return name;
  }

  /**
   * 从混合空间更新混合层
   * @param blendSpaceId 混合空间ID
   * @param position 位置
   * @param nodes 节点列表
   */
  public updateFromBlendSpace(
    blendSpaceId: string,
    position: number | { x: number; y: number },
    nodes: Array<{ clipName: string; weight: number }>
  ): void {
    if (!this.enabled) return;

    // 移除之前的混合空间层
    this.layers = this.layers.filter(layer => !layer.clipName.startsWith(`blendSpace_${blendSpaceId}_`));

    // 添加新的混合空间层
    for (const node of nodes) {
      if (node.weight > 0) {
        this.addLayer(
          node.clipName,
          node.weight,
          BlendMode.OVERRIDE,
          1.0
        );
      }
    }

    // 发出混合空间更新事件
    this.eventEmitter.emit(BlendEventType.BLEND_SPACE_UPDATE, {
      blendSpaceId,
      position,
      nodes
    });

    // 应用混合层
    this.applyLayers();
  }

  /**
   * 从1D混合空间更新混合层
   * @param blendSpaceId 混合空间ID
   * @param position 位置
   * @param minValue 最小值
   * @param maxValue 最大值
   * @param clips 动画片段列表
   * @param positions 位置列表
   */
  public updateFromBlendSpace1D(
    blendSpaceId: string,
    position: number,
    minValue: number,
    maxValue: number,
    clips: string[],
    positions: number[]
  ): void {
    if (!this.enabled || clips.length === 0) return;

    // 计算权重
    const weights = new Array(clips.length).fill(0);

    // 如果只有一个片段，则权重为1
    if (clips.length === 1) {
      weights[0] = 1;
    } else {
      // 找到位置两侧的片段
      let leftIndex = 0;
      let rightIndex = 0;

      // 按位置排序
      const sortedIndices = positions.map((_, i) => i)
        .sort((a, b) => positions[a] - positions[b]);

      // 如果位置小于最小值，则使用最小的两个片段
      if (position <= positions[sortedIndices[0]]) {
        leftIndex = sortedIndices[0];
        rightIndex = sortedIndices[1];
      }
      // 如果位置大于最大值，则使用最大的两个片段
      else if (position >= positions[sortedIndices[sortedIndices.length - 1]]) {
        leftIndex = sortedIndices[sortedIndices.length - 2];
        rightIndex = sortedIndices[sortedIndices.length - 1];
      }
      // 否则，找到位置两侧的片段
      else {
        for (let i = 0; i < sortedIndices.length - 1; i++) {
          const current = sortedIndices[i];
          const next = sortedIndices[i + 1];

          if (positions[current] <= position && positions[next] >= position) {
            leftIndex = current;
            rightIndex = next;
            break;
          }
        }
      }

      // 计算权重
      const range = positions[rightIndex] - positions[leftIndex];

      if (Math.abs(range) < 0.0001) {
        // 如果范围太小，则平均分配权重
        weights[leftIndex] = 0.5;
        weights[rightIndex] = 0.5;
      } else {
        // 线性插值
        const t = (position - positions[leftIndex]) / range;
        weights[leftIndex] = 1 - t;
        weights[rightIndex] = t;
      }
    }

    // 创建节点列表
    const nodes = clips.map((clipName, i) => ({
      clipName,
      weight: weights[i]
    }));

    // 更新混合层
    this.updateFromBlendSpace(blendSpaceId, position, nodes);
  }

  /**
   * 从2D混合空间更新混合层
   * @param blendSpaceId 混合空间ID
   * @param position 位置
   * @param minX 最小X值
   * @param maxX 最大X值
   * @param minY 最小Y值
   * @param maxY 最大Y值
   * @param clips 动画片段列表
   * @param positions 位置列表
   * @param useTriangulation 是否使用三角形混合
   */
  public updateFromBlendSpace2D(
    blendSpaceId: string,
    position: { x: number; y: number },
    minX: number,
    maxX: number,
    minY: number,
    maxY: number,
    clips: string[],
    positions: Array<{ x: number; y: number }>,
    useTriangulation: boolean = true
  ): void {
    if (!this.enabled || clips.length === 0) return;

    // 计算权重
    const weights = new Array(clips.length).fill(0);

    // 如果只有一个片段，则权重为1
    if (clips.length === 1) {
      weights[0] = 1;
    }
    // 如果使用三角形混合且有足够的片段
    else if (useTriangulation && clips.length >= 3) {
      // 找到最近的三个片段
      const distances = positions.map((pos, i) => {
        const dx = pos.x - position.x;
        const dy = pos.y - position.y;
        return {
          index: i,
          distance: Math.sqrt(dx * dx + dy * dy)
        };
      });

      // 按距离排序
      distances.sort((a, b) => a.distance - b.distance);

      // 取最近的三个片段
      const nearestIndices = distances.slice(0, 3).map(d => d.index);

      // 计算权重（简化为基于距离的反比例）
      let totalWeight = 0;

      for (let i = 0; i < nearestIndices.length; i++) {
        const index = nearestIndices[i];
        const distance = distances[i].distance;

        // 避免除以零
        if (distance < 0.0001) {
          weights[index] = 1;
          totalWeight = 1;
          break;
        }

        weights[index] = 1 / distance;
        totalWeight += weights[index];
      }

      // 归一化权重
      if (totalWeight > 0) {
        for (let i = 0; i < nearestIndices.length; i++) {
          const index = nearestIndices[i];
          weights[index] /= totalWeight;
        }
      }
    }
    // 否则，使用距离权重
    else {
      // 计算每个片段到当前位置的距离
      let totalWeight = 0;

      for (let i = 0; i < positions.length; i++) {
        const pos = positions[i];
        const dx = pos.x - position.x;
        const dy = pos.y - position.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 避免除以零
        if (distance < 0.0001) {
          weights[i] = 1000;
        } else {
          weights[i] = 1 / (distance * distance);
        }

        totalWeight += weights[i];
      }

      // 归一化权重
      if (totalWeight > 0) {
        for (let i = 0; i < weights.length; i++) {
          weights[i] /= totalWeight;
        }
      }
    }

    // 创建节点列表
    const nodes = clips.map((clipName, i) => ({
      clipName,
      weight: weights[i]
    }));

    // 更新混合层
    this.updateFromBlendSpace(blendSpaceId, position, nodes);
  }

  /**
   * 集成物理系统
   * @param entity 实体
   * @param physicsSystem 物理系统
   * @param config 配置
   * @returns 物理动画集成
   */
  public integratePhysics(
    entity: Entity,
    physicsSystem: any,
    config: any = {}
  ): PhysicsAnimationIntegration {
    // 创建物理动画集成
    this.physicsIntegration = new PhysicsAnimationIntegration(
      entity,
      physicsSystem,
      this,
      config
    );

    // 初始化物理动画集成
    this.physicsIntegration.initialize();

    // 触发物理集成事件
    this.eventEmitter.emit(BlendEventType.PHYSICS_INTEGRATED, {
      entity,
      physicsSystem,
      integration: this.physicsIntegration
    });

    return this.physicsIntegration;
  }

  /**
   * 集成输入系统
   * @param entity 实体
   * @param inputSystem 输入系统
   * @param config 配置
   * @returns 输入动画集成
   */
  public integrateInput(
    entity: Entity,
    inputSystem: any,
    config: any = {}
  ): InputAnimationIntegration {
    // 创建输入动画集成
    this.inputIntegration = new InputAnimationIntegration(
      entity,
      inputSystem,
      this,
      config
    );

    // 初始化输入动画集成
    this.inputIntegration.initialize();

    // 触发输入集成事件
    this.eventEmitter.emit(BlendEventType.INPUT_INTEGRATED, {
      entity,
      inputSystem,
      integration: this.inputIntegration
    });

    return this.inputIntegration;
  }

  /**
   * 获取物理动画集成
   * @returns 物理动画集成
   */
  public getPhysicsIntegration(): PhysicsAnimationIntegration | undefined {
    return this.physicsIntegration;
  }

  /**
   * 获取输入动画集成
   * @returns 输入动画集成
   */
  public getInputIntegration(): InputAnimationIntegration | undefined {
    return this.inputIntegration;
  }

  /**
   * 压缩动画片段
   * @param clipName 动画片段名称
   * @returns 压缩结果
   */
  public compressAnimation(clipName: string): any {
    const clip = this.animator.getClip(clipName);
    if (!clip) {
      this.debugger.logWarning('compression', `动画片段 "${clipName}" 不存在`);
      return null;
    }

    const result = this.compressor.compressClip(clip as any);
    this.debugger.log('info', 'compression', `动画压缩完成: ${clipName}`, result.stats);

    return result;
  }

  /**
   * 预加载动画
   * @param url 动画URL
   * @param priority 优先级
   * @returns 预加载项ID
   */
  public preloadAnimation(url: string, priority: number = 0): string {
    const id = `anim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    this.preloader.addItem({
      id,
      url,
      priority,
      tags: ['animation']
    });

    this.debugger.log('info', 'preloader', `动画预加载已添加: ${url}`, { id, priority });

    return id;
  }

  /**
   * 获取预加载的动画
   * @param id 预加载项ID
   * @returns 动画片段
   */
  public getPreloadedAnimation(id: string): any {
    const clip = this.preloader.getClip(id);
    if (clip) {
      this.debugger.log('info', 'preloader', `获取预加载动画: ${id}`);
    } else {
      this.debugger.logWarning('preloader', `预加载动画不存在: ${id}`);
    }
    return clip;
  }

  /**
   * 启用网络同步
   * @param serverUrl 服务器URL
   */
  public enableNetworkSync(serverUrl: string): void {
    if (!this.synchronizer) {
      this.synchronizer = new AnimationSynchronizer({
        serverUrl,
        debug: this.debugger.getConfig().mode !== DebugMode.NONE
      });

      // 设置同步事件监听
      this.synchronizer.on('animationStart', (data: any) => {
        this.debugger.log('info', 'sync', `远程动画开始: ${data.animationName}`, data);
        // 这里可以同步播放动画
      });

      this.synchronizer.on('animationStop', (data: any) => {
        this.debugger.log('info', 'sync', `远程动画停止: ${data.animationName}`, data);
        // 这里可以同步停止动画
      });

      this.synchronizer.connect();
      this.debugger.log('info', 'sync', `网络同步已启用: ${serverUrl}`);
    }
  }

  /**
   * 禁用网络同步
   */
  public disableNetworkSync(): void {
    if (this.synchronizer) {
      this.synchronizer.disconnect();
      this.synchronizer = null;
      this.debugger.log('info', 'sync', '网络同步已禁用');
    }
  }

  /**
   * 设置调试模式
   * @param mode 调试模式
   */
  public setDebugMode(mode: DebugMode): void {
    this.debugger.enable(mode);
    this.debugger.log('info', 'debugger', `调试模式已设置: ${mode}`);
  }

  /**
   * 设置质量等级
   * @param quality 质量等级
   */
  public setQualityLevel(quality: QualityLevel): void {
    this.qualityController.setQuality(quality);
    this.debugger.log('info', 'quality', `质量等级已设置: ${quality}`);
  }

  /**
   * 获取系统状态
   * @returns 系统状态信息
   */
  public getSystemStatus(): any {
    return {
      blender: {
        layersCount: this.layers.length,
        masksCount: this.masks.size,
        cacheEnabled: this.cacheEnabled,
        blendQuality: this.blendQuality
      },
      eventSystem: {
        isRunning: this.eventSystem ? true : false,
        config: this.eventSystem?.getConfig()
      },
      preloader: {
        stats: this.preloader.getStats()
      },
      synchronizer: {
        connected: this.synchronizer?.getState() === 'connected',
        state: this.synchronizer?.getState()
      },
      debugger: {
        mode: this.debugger.getConfig().mode,
        enabled: this.debugger.getConfig().mode !== DebugMode.NONE
      },
      qualityController: {
        currentQuality: this.qualityController.getQuality(),
        devicePerformance: this.qualityController.getDevicePerformance(),
        stats: this.qualityController.getAIPerformanceStats()
      }
    };
  }

  /**
   * 创建高级子片段
   * @param name 子片段名称
   * @param originalClipName 原始剪辑名称
   * @param startTime 开始时间（秒）
   * @param endTime 结束时间（秒）
   * @param loop 是否循环
   * @param reverse 是否反向播放
   * @param timeScale 播放速度
   * @returns 高级子片段
   */
  public createAdvancedSubClip(
    name: string,
    originalClipName: string,
    startTime: number = 0,
    endTime: number = 0,
    loop: boolean = false,
    reverse: boolean = false,
    timeScale: number = 1.0
  ): AnimationSubClip {
    // 创建子片段
    const subClip = new AnimationSubClip({
      name,
      originalClipName,
      startTime,
      endTime,
      loop,
      reverse,
      timeScale
    });

    // 添加到映射
    this.advancedSubClips.set(name, subClip);

    // 获取原始剪辑
    const originalClip = this.animator.getClip(originalClipName);
    if (originalClip) {
      // 设置原始剪辑
      const threeOriginalClip = AnimationClipAdapter.ensureThreeClip(originalClip);
      subClip.setOriginalClip(threeOriginalClip);

      // 获取子片段
      const clip = subClip.getSubClip();

      // 添加到动画控制器
      if (clip) {
        const customClip = AnimationClipAdapter.ensureCustomClip(clip);
        this.animator.addClip(customClip);
      }

      // 触发子片段创建事件
      this.eventEmitter.emit(BlendEventType.SUBCLIP_CREATED, {
        name,
        originalClipName,
        startTime,
        endTime,
        loop,
        reverse,
        timeScale
      });
    } else {
      console.warn(`原始剪辑 "${originalClipName}" 不存在`);
    }

    return subClip;
  }

  /**
   * 获取高级子片段
   * @param name 子片段名称
   * @returns 高级子片段
   */
  public getAdvancedSubClip(name: string): AnimationSubClip | undefined {
    return this.advancedSubClips.get(name);
  }

  /**
   * 移除高级子片段
   * @param name 子片段名称
   * @returns 是否成功移除
   */
  public removeAdvancedSubClip(name: string): boolean {
    // 获取子片段
    const subClip = this.advancedSubClips.get(name);
    if (!subClip) {
      return false;
    }

    // 获取子片段的子片段
    const clip = subClip.getSubClip();
    if (clip) {
      // 移除动画片段
      this.animator.removeClip(clip.name);
    }

    // 移除子片段
    return this.advancedSubClips.delete(name);
  }

  /**
   * 创建嵌套子片段
   * @param name 子片段名称
   * @param parentName 父子片段名称
   * @param startTime 开始时间（秒）
   * @param endTime 结束时间（秒）
   * @param loop 是否循环
   * @param reverse 是否反向播放
   * @param timeScale 播放速度
   * @returns 嵌套子片段
   */
  public createNestedSubClip(
    name: string,
    parentName: string,
    startTime: number = 0,
    endTime: number = 0,
    loop: boolean = false,
    reverse: boolean = false,
    timeScale: number = 1.0
  ): AnimationSubClip | null {
    // 获取父子片段
    const parentSubClip = this.advancedSubClips.get(parentName);
    if (!parentSubClip) {
      console.warn(`父子片段 "${parentName}" 不存在`);
      return null;
    }

    // 获取父子片段的子片段
    const parentClip = parentSubClip.getSubClip();
    if (!parentClip) {
      console.warn(`父子片段 "${parentName}" 的子片段不存在`);
      return null;
    }

    // 创建子片段
    const subClip = new AnimationSubClip({
      name,
      originalClipName: parentClip.name,
      startTime,
      endTime,
      loop,
      reverse,
      timeScale
    });

    // 设置原始剪辑
    subClip.setOriginalClip(parentClip);

    // 添加到父子片段
    parentSubClip.addSubClip(subClip);

    // 添加到映射
    this.advancedSubClips.set(name, subClip);

    // 获取子片段
    const clip = subClip.getSubClip();

    // 添加到动画控制器
    if (clip) {
      const customClip = AnimationClipAdapter.ensureCustomClip(clip);
      this.animator.addClip(customClip);
    }

    // 触发子片段创建事件
    this.eventEmitter.emit(BlendEventType.SUBCLIP_CREATED, {
      name,
      parentName,
      startTime,
      endTime,
      loop,
      reverse,
      timeScale,
      nested: true
    });

    return subClip;
  }

  /**
   * 混合子片段
   * @param name 混合后的子片段名称
   * @param subClipNames 子片段名称列表
   * @param weights 权重列表
   * @returns 混合后的子片段
   */
  public blendSubClips(
    name: string,
    subClipNames: string[],
    weights?: number[]
  ): AnimationSubClip | null {
    if (subClipNames.length === 0) {
      console.warn('子片段名称列表为空');
      return null;
    }

    // 获取第一个子片段
    const firstSubClip = this.advancedSubClips.get(subClipNames[0]);
    if (!firstSubClip) {
      console.warn(`子片段 "${subClipNames[0]}" 不存在`);
      return null;
    }

    // 创建混合后的子片段
    const blendedSubClip = new AnimationSubClip({
      name,
      originalClipName: firstSubClip.getOriginalClipName(),
      startTime: firstSubClip.getStartTime(),
      endTime: firstSubClip.getEndTime(),
      loop: firstSubClip.getLoop(),
      reverse: firstSubClip.getReverse(),
      timeScale: firstSubClip.getTimeScale()
    });

    // 添加子片段
    for (let i = 0; i < subClipNames.length; i++) {
      const subClipName = subClipNames[i];
      const subClip = this.advancedSubClips.get(subClipName);

      if (subClip) {
        blendedSubClip.addSubClip(subClip);
      } else {
        console.warn(`子片段 "${subClipName}" 不存在`);
      }
    }

    // 获取第一个子片段的子片段
    const firstClip = firstSubClip.getSubClip();
    if (!firstClip) {
      console.warn(`子片段 "${subClipNames[0]}" 的子片段不存在`);
      return null;
    }

    // 设置原始剪辑
    blendedSubClip.setOriginalClip(firstClip);

    // 添加到映射
    this.advancedSubClips.set(name, blendedSubClip);

    // 混合子片段
    const blendedClip = blendedSubClip.blendSubClips(weights ? weights[0] : 0.5);

    // 添加到动画控制器
    if (blendedClip) {
      const customBlendedClip = AnimationClipAdapter.ensureCustomClip(blendedClip);
      this.animator.addClip(customBlendedClip);
    }

    // 触发子片段创建事件
    this.eventEmitter.emit(BlendEventType.SUBCLIP_CREATED, {
      name,
      subClipNames,
      weights,
      blended: true
    });

    return blendedSubClip;
  }

  /**
   * 设置缓存启用状态
   */
  public setCacheEnabled(enabled: boolean): void {
    // 实现缓存启用逻辑
    console.log(`设置缓存启用状态: ${enabled}`);
  }

  /**
   * 设置对象池启用状态
   */
  public setObjectPoolEnabled(enabled: boolean): void {
    // 实现对象池启用逻辑
    console.log(`设置对象池启用状态: ${enabled}`);
  }

  /**
   * 设置批处理启用状态
   */
  public setBatchProcessingEnabled(enabled: boolean): void {
    // 实现批处理启用逻辑
    console.log(`设置批处理启用状态: ${enabled}`);
  }

  /**
   * 设置层级
   */
  public setLayers(layers: any[]): void {
    // 实现层级设置逻辑
    console.log(`设置层级:`, layers);
  }

  /**
   * 获取遮罩列表
   */
  public getMasks(): AnimationMask[] {
    return Array.from(this.masks.values());
  }
}
